<template>
  <div
    v-if="!inversion"
    class="rounded-lg p-4"
    style="width: 550px"
    ref="textDemo"
  >
    <div class="think-time" @click="show = !show">
      深度思考（用时{{ duration }}秒）
      <i class="el-icon-arrow-down" v-if="show"></i>
      <i class="el-icon-arrow-right" v-else></i>
    </div>
    <el-collapse-transition>
      <div v-show="show">
        <p class="think-desc">
          <!-- {{ desc }} -->

          {{ chartData.reason }}
        </p>
      </div>
    </el-collapse-transition>
     <div class=" mt-4 ">
     {{ chartData.answer }}
</div>

    <div v-for="(item, index) in chartData.data" :key="index">
      <div class="mb-4">
        <p class="text-gray-800 mb-4 mt-4  font-bold">以下是{{ item.indicator }}情况:</p>

        <div class="bg-gray-50 p-4 rounded-lg shadow-sm">
          <div class="text-gray-600 text-sm">
            <span class="font-bold">过滤维度：</span>
            {{ item.dimSearch || "-" }}
          </div>
          <div class="text-gray-600 text-sm mt-4">
            <span class="font-bold">分组维度：</span>
            {{ item.dimGroup || "-" }}
          </div>
          <div>
            <el-radio-group
              v-model="type"
              class="flex justify-end mt-2 mb-2"
              v-if="item.dim.length === 1"
            >
              <el-radio-button label="CommonTable" value="CommonTable">
                表格
              </el-radio-button>
              <el-radio-button label="ChartLine" value="ChartLine">
                折线图
              </el-radio-button>
              <el-radio-button label="ChartColumn" value="ChartColumn">
                柱状图
              </el-radio-button>
              <el-radio-button label="ChartPie" value="ChartPie">
                饼图
              </el-radio-button>
            </el-radio-group>
          </div>

          <div class="chart-box">
            <ChartPie
              v-if="type === 'ChartPie'"
              :chart-data="item.data"
              :color-field="item.dim[0]"
              :angle-field="item.indicator"
              series-name=""
              :unit="item.indUnit || ''"
            />
            <ChartLine
              v-if="type === 'ChartLine'"
              :chart-data="item.data"
              :x-field="item.dim[0]"
              :y-field="item.indicator"
              :series-name="[]"
              :y-axis-name="item.indUnit ? `单位 (${item.indUnit})` : ''"
              :unit="item.indUnit || ''"
              is-formatter-x-axis
            />
            <ChartColumn
              v-if="type === 'ChartColumn'"
              :chart-data="item.data"
              :x-field="item.dim[0]"
              :y-field="item.indicator"
              :series-name="[]"
              :y-axis-name="item.indUnit ? `单位 (${item.indUnit})` : ''"
              :unit="item.indUnit || ''"
              is-formatter-x-axis
            />
            <CommonTable
              v-if="type === 'CommonTable'"
              border
              height="220"
              :table-data="item.data"
              :show-selection="false"
              :show-batch-tag="false"
              :table-columns="[
                {
                  prop: item.indicator,
                  label: item.indicator,
                  visible: true,
                  sortable: false
                },
                ...item.dim.map(item => ({
                  prop: item,
                  label: item,
                  visible: true,
                  sortable: false
                }))
              ]"
            ></CommonTable>
          </div>
        </div>
      </div>
      <div class="flex items-center justify-between text-sm">
        <div class="text-gray-500">
          <span class="font-bold">相关链接:</span>
          <a
            :href="item.indUrl"
            target="_blank"
            class="text-blue-500 hover:underline"
          >
            {{ item.indicator }}
          </a>
        </div>
        <div class="flex space-x-3">
          <el-tooltip class="item" effect="dark" content="下载" placement="top">
            <div class="btn-item download" @click="exportImg"></div>
          </el-tooltip>
          <el-tooltip class="item" effect="dark" content="复制" placement="top">
            <div class="btn-item copy" @click="copyAllText"></div>
          </el-tooltip>
          <el-tooltip class="item" effect="dark" content="赞同" placement="top">
            <div class="btn-item praise"></div>
          </el-tooltip>
          <el-tooltip
            class="item"
            effect="dark"
            content="不赞同"
            placement="top"
          >
            <div class="btn-item hate"></div>
          </el-tooltip>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import html2canvas from "html2canvas"

import ChartPie from "@/components/Charts/ChartPie.vue"
import ChartColumn from "@/components/Charts/ChartColumn.vue"
import ChartLine from "@/components/Charts/ChartLine.vue"
import CommonTable from "@/components/CommonTable.vue"
export default {
  components: {
    ChartPie,
    ChartColumn,
    ChartLine,
    CommonTable
  },
  props: {
    chartData: {
      type: Object,
      default: () => {}
    },
    duration: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      show: true,
      type: "CommonTable",
      chartData1: [
        {
          "专任教师人数_性别、职级": "2561",
          统计年份: "2023"
        },
        {
          "专任教师人数_性别、职级": "2562",
          统计年份: "2021"
        },
        {
          "专任教师人数_性别、职级": "2556",
          统计年份: "2024"
        },
        {
          "专任教师人数_性别、职级": "2577",
          统计年份: "2022"
        }
      ],

      desc: "检索指标库，未找到相关指标，我将尝试基于数据源计算。 对于这个问题，我进行了如下拆解：1. 目标指标：各学院学生的英语课程平均成绩（所有时间全量的数据）；2. 子指标：各学院学生的总成绩，各学院学生选课的总人数；3. 指标计算过程：首先从学生成绩信息表中获取各学院学生的总成绩和选课的总人数。然后，通过将总成绩除以总人数，计算出各学院学生的课程平均成绩。 我将从学生基本信息、学生成绩信息、部门、院系基本信息、课程基本信息中进行数据查询。"
    }
  },
  computed: {
    tableColumns() {
      if (this.chartData.dimGroup) {
        this.tableColumns[1].prop = this.chartData.dimGroup
      }
      if (this.chartData.indicator) {
        this.tableColumns[2].prop = this.chartData.indicator
      }
      return this.tableColumns
    }
  },
  created() {},
  mounted() {},
  watch: {},
  methods: {
    exportImg() {
      const content = this.$refs.textDemo
      console.log(content, "content")
      html2canvas(content, {
        scale: 2, // 放大倍数
        useCORS: true // 开启跨域配置，但和allowTaint不能共存
      }).then(canvas => {
        let dataURL = canvas.toDataURL("image/jpg")
        let link = document.createElement("a")
        link.href = dataURL
        let filename = +new Date() + ".png" // 文件名称
        link.setAttribute("download", filename)
        link.style.display = "none" // a标签隐藏
        document.body.appendChild(link)
        link.click()
      })
    },
    getAllText(element) {
      let text = ""

      // 递归遍历所有子节点
      const walk = node => {
        if (node.nodeType === Node.TEXT_NODE) {
          text += node.textContent
        } else if (
          node.nodeType === Node.ELEMENT_NODE &&
          !["SCRIPT", "STYLE"].includes(node.tagName)
        ) {
          Array.from(node.childNodes).forEach(walk)
        }
      }

      walk(element)
      return text.replace(/\s+/g, " ").trim() // 清理多余空格
    },

    // 修改复制方法
    async copyAllText() {
      // 后续粘贴逻辑同上
      const text = this.getAllText(this.$refs.textDemo)
      // // 支持 Clipboard API
      if (navigator.clipboard) {
        navigator.clipboard
          .writeText(text)
          .then(() => {
            this.$message.success("复制成功")
          })
          .catch(err => {
            console.error("Clipboard API 失败，尝试回退方法:", err)
            this.fallbackCopy(text) // 失败时使用回退方案
          })
      } else {
        // 不支持则直接使用回退
        this.fallbackCopy(text)
      }
    },
    fallbackCopy(text) {
      // 创建临时文本域并选中内容
      const textarea = document.createElement("textarea")
      textarea.value = text
      textarea.setAttribute("readonly", "") // 防止键盘弹出（移动端）
      textarea.style.position = "absolute"
      textarea.style.left = "-9999px"
      document.body.appendChild(textarea)
      textarea.select()

      try {
        document.execCommand("copy")
        this.$message.success("复制成功")
      } catch (err) {
        console.error("回退复制失败:", err)
      }

      // 清理DOM
      document.body.removeChild(textarea)
    }
  }
}
</script>

<style scoped lang="scss">
.think-time {
  display: flex;
  justify-content: space-between;
  padding: 0 20px;
  align-items: center;
  height: 27px;
  background: #3875f6;
  border-radius: 27px;
  font-family: Source Han Sans SC, Source Han Sans SC;
  font-weight: 400;
  font-size: 14px;
  color: #ffffff;
  line-height: 27px;
  cursor: pointer;
  width: max-content;
}
.think-desc {
  font-family: Source Han Sans SC, Source Han Sans SC;
  font-weight: 400;
  font-size: 12px;
  color: #6a6a6a;
  line-height: 18px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  border-left: 3px solid #c8c8c8;
  padding-left: 10px;
  margin: 10px 5px 0 10px;
}
.chart-box {
  width: 100%;
  height: 220px;
}

.btn-item {
  width: 25px;
  height: 25px;
  cursor: pointer;

  &:hover {
    transform: scale(1.1);
  }

  &.download {
    background: url("~@/assets/images/ai/download.png") no-repeat center;
    background-size: cover;
  }

  &.copy {
    background: url("~@/assets/images/ai/copy.png") no-repeat center;
    background-size: cover;
  }

  &.praise {
    background: url("~@/assets/images/ai/praise.png") no-repeat center;
    background-size: cover;
  }

  &.hate {
    background: url("~@/assets/images/ai/hate.png") no-repeat center;
    background-size: cover;
  }
}
</style>
