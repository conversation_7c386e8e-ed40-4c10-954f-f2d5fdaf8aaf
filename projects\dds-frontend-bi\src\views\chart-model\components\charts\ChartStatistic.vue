<template>
  <div class="chart-statistic" :style="{ width, height }">
    <div class="statistic-container" :class="containerClass">
      <!-- 图标 -->
      <div v-if="icon" class="statistic-icon" :style="iconStyle">
        <i :class="icon"></i>
      </div>
      
      <!-- 主要内容 -->
      <div class="statistic-content">
        <!-- 标题 -->
        <div v-if="label" class="statistic-label" :style="labelStyle">
          {{ label }}
        </div>
        
        <!-- 数值 -->
        <div class="statistic-value-container">
          <span v-if="prefix" class="statistic-prefix" :style="prefixStyle">
            {{ prefix }}
          </span>
          <span class="statistic-value" :style="finalValueStyle">
            {{ displayValue }}
          </span>
          <span v-if="unit" class="statistic-unit" :style="unitStyle">
            {{ unit }}
          </span>
        </div>
        
        <!-- 描述信息 -->
        <div v-if="description" class="statistic-description" :style="descriptionStyle">
          {{ description }}
        </div>
        
        <!-- 趋势指示器 -->
        <div v-if="showTrend && trend" class="statistic-trend" :class="trendClass">
          <i :class="trendIcon"></i>
          <span class="trend-text">{{ trendText }}</span>
        </div>
        
        <!-- 额外信息 -->
        <div v-if="extra" class="statistic-extra" :style="extraStyle">
          {{ extra }}
        </div>
      </div>
      
      <!-- 右侧内容 -->
      <div v-if="$slots.suffix" class="statistic-suffix">
        <slot name="suffix"></slot>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ChartStatistic',
  props: {
    // 基础属性
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '120px'
    },
    // 数值配置
    value: {
      type: [Number, String],
      default: 0
    },
    label: {
      type: String,
      default: ''
    },
    unit: {
      type: String,
      default: ''
    },
    prefix: {
      type: String,
      default: ''
    },
    precision: {
      type: Number,
      default: 0
    },
    // 样式配置
    valueStyle: {
      type: Object,
      default: () => ({
        color: '#1890ff',
        fontSize: '24px',
        fontWeight: 'bold'
      })
    },
    labelStyle: {
      type: Object,
      default: () => ({
        color: '#666666',
        fontSize: '14px',
        marginBottom: '8px'
      })
    },
    prefixStyle: {
      type: Object,
      default: () => ({
        color: '#1890ff',
        fontSize: '20px',
        fontWeight: 'bold',
        marginRight: '4px'
      })
    },
    unitStyle: {
      type: Object,
      default: () => ({
        color: '#999999',
        fontSize: '16px',
        marginLeft: '4px'
      })
    },
    descriptionStyle: {
      type: Object,
      default: () => ({
        color: '#999999',
        fontSize: '12px',
        marginTop: '4px'
      })
    },
    extraStyle: {
      type: Object,
      default: () => ({
        color: '#666666',
        fontSize: '12px',
        marginTop: '8px'
      })
    },
    // 图标配置
    icon: {
      type: String,
      default: ''
    },
    iconStyle: {
      type: Object,
      default: () => ({
        color: '#1890ff',
        fontSize: '24px',
        marginRight: '12px'
      })
    },
    // 趋势配置
    showTrend: {
      type: Boolean,
      default: false
    },
    trend: {
      type: String,
      default: '' // 'up', 'down', 'flat'
    },
    trendValue: {
      type: [Number, String],
      default: ''
    },
    // 格式化配置
    formatter: {
      type: Function,
      default: null
    },
    thousandsSeparator: {
      type: Boolean,
      default: true
    },
    // 布局配置
    layout: {
      type: String,
      default: 'vertical' // 'vertical', 'horizontal'
    },
    // 主题配置
    theme: {
      type: String,
      default: 'default' // 'default', 'card', 'minimal'
    },
    // 描述信息
    description: {
      type: String,
      default: ''
    },
    extra: {
      type: String,
      default: ''
    },
    // 动画配置
    animation: {
      type: Boolean,
      default: true
    },
    animationDuration: {
      type: Number,
      default: 1000
    }
  },
  data() {
    return {
      currentValue: 0,
      animationTimer: null
    }
  },
  computed: {
    displayValue() {
      let val = this.currentValue
      
      // 自定义格式化函数
      if (this.formatter && typeof this.formatter === 'function') {
        return this.formatter(val)
      }
      
      // 精度处理
      if (this.precision > 0) {
        val = Number(val).toFixed(this.precision)
      } else {
        val = Math.round(Number(val))
      }
      
      // 千分位分隔符
      if (this.thousandsSeparator) {
        val = this.addThousandsSeparator(val.toString())
      }
      
      return val.toString()
    },
    finalValueStyle() {
      return {
        ...this.valueStyle,
        transition: this.animation ? `all ${this.animationDuration}ms ease` : 'none'
      }
    },
    containerClass() {
      return [
        `statistic-${this.layout}`,
        `statistic-${this.theme}`,
        {
          'has-icon': !!this.icon,
          'has-trend': this.showTrend && this.trend
        }
      ]
    },
    trendClass() {
      return [
        'trend',
        `trend-${this.trend}`
      ]
    },
    trendIcon() {
      switch (this.trend) {
        case 'up':
          return 'el-icon-top'
        case 'down':
          return 'el-icon-bottom'
        case 'flat':
          return 'el-icon-minus'
        default:
          return ''
      }
    },
    trendText() {
      if (this.trendValue) {
        return this.trendValue
      }
      switch (this.trend) {
        case 'up':
          return '上升'
        case 'down':
          return '下降'
        case 'flat':
          return '持平'
        default:
          return ''
      }
    }
  },
  watch: {
    value: {
      handler(newVal) {
        if (this.animation) {
          this.animateValue(newVal)
        } else {
          this.currentValue = newVal
        }
      },
      immediate: true
    }
  },
  beforeUnmount() {
    if (this.animationTimer) {
      cancelAnimationFrame(this.animationTimer)
    }
  },
  methods: {
    animateValue(targetValue) {
      if (this.animationTimer) {
        cancelAnimationFrame(this.animationTimer)
      }
      
      const startValue = this.currentValue
      const endValue = Number(targetValue)
      const duration = this.animationDuration
      const startTime = Date.now()
      
      const animate = () => {
        const now = Date.now()
        const progress = Math.min((now - startTime) / duration, 1)
        
        // 使用缓动函数
        const easeProgress = this.easeOutCubic(progress)
        this.currentValue = startValue + (endValue - startValue) * easeProgress
        
        if (progress < 1) {
          this.animationTimer = requestAnimationFrame(animate)
        } else {
          this.currentValue = endValue
          this.animationTimer = null
        }
      }
      
      this.animationTimer = requestAnimationFrame(animate)
    },
    easeOutCubic(t) {
      return 1 - Math.pow(1 - t, 3)
    },
    addThousandsSeparator(num) {
      const parts = num.toString().split('.')
      parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',')
      return parts.join('.')
    },
    // 公共方法
    updateValue(newValue) {
      if (this.animation) {
        this.animateValue(newValue)
      } else {
        this.currentValue = newValue
      }
    },
    getValue() {
      return this.currentValue
    },
    reset() {
      this.currentValue = 0
    }
  }
}
</script>

<style scoped>
.chart-statistic {
  position: relative;
}

.statistic-container {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 16px;
  box-sizing: border-box;
}

.statistic-container.statistic-vertical {
  flex-direction: column;
  text-align: center;
}

.statistic-container.statistic-horizontal {
  flex-direction: row;
  text-align: left;
}

.statistic-container.statistic-card {
  background: #ffffff;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.statistic-container.statistic-minimal {
  padding: 8px;
}

.statistic-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #1890ff;
  font-size: 24px;
}

.statistic-vertical .statistic-icon {
  margin-bottom: 8px;
}

.statistic-horizontal .statistic-icon {
  margin-right: 12px;
}

.statistic-content {
  flex: 1;
}

.statistic-label {
  color: #666666;
  font-size: 14px;
  margin-bottom: 8px;
}

.statistic-value-container {
  display: flex;
  align-items: baseline;
  justify-content: center;
}

.statistic-horizontal .statistic-value-container {
  justify-content: flex-start;
}

.statistic-prefix {
  color: #1890ff;
  font-size: 20px;
  font-weight: bold;
  margin-right: 4px;
}

.statistic-value {
  color: #1890ff;
  font-size: 24px;
  font-weight: bold;
  font-family: 'Arial', 'Helvetica', sans-serif;
  line-height: 1;
}

.statistic-unit {
  color: #999999;
  font-size: 16px;
  margin-left: 4px;
}

.statistic-description {
  color: #999999;
  font-size: 12px;
  margin-top: 4px;
}

.statistic-trend {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 8px;
  font-size: 12px;
}

.statistic-horizontal .statistic-trend {
  justify-content: flex-start;
}

.trend-up {
  color: #52c41a;
}

.trend-down {
  color: #f5222d;
}

.trend-flat {
  color: #faad14;
}

.trend-text {
  margin-left: 4px;
}

.statistic-extra {
  color: #666666;
  font-size: 12px;
  margin-top: 8px;
}

.statistic-suffix {
  margin-left: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .statistic-value {
    font-size: 20px;
  }
  
  .statistic-prefix {
    font-size: 16px;
  }
  
  .statistic-unit {
    font-size: 14px;
  }
}
</style>
