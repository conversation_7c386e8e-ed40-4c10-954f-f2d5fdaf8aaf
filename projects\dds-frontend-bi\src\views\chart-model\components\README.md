# 图表组件映射配置

本文件提供了12种图表组件的完整配置，包括组件映射、静态测试数据和属性配置。

## 支持的图表类型

### 1. 数据展示类
- **table** - 表格组件
  - 组件路径: `@/components/CommonTable.vue`
  - 用途: 展示结构化数据

### 2. 基础图表类
- **bar** - 柱状图
  - 组件路径: `@/components/Charts/ChartColumn.vue`
  - 用途: 展示分类数据的数值比较

- **horizontalBar** - 条形图
  - 组件路径: `@/components/Charts/ChartBar.vue`
  - 用途: 水平方向展示分类数据

- **line** - 折线图
  - 组件路径: `@/components/Charts/ChartLine.vue`
  - 用途: 展示数据随时间的变化趋势

- **pie** - 饼图
  - 组件路径: `@/components/Charts/ChartPie.vue`
  - 用途: 展示数据的占比关系

### 3. 复合图表类
- **lineBar** - 双轴图（折线+柱状）
  - 组件路径: `@/components/Charts/ChartDoubleAxis.vue`
  - 用途: 同时展示两种不同量级的数据

### 4. 特殊图表类
- **radar** - 雷达图
  - 组件路径: `@/components/Charts/ChartRadar.vue`
  - 用途: 多维度数据对比分析

### 5. 数值展示类
- **flop** - 翻牌器
  - 组件路径: `@/components/Charts/ChartPictorialBar.vue`
  - 用途: 动态展示重要数值

- **statistic** - 统计数值
  - 组件路径: `@/components/Charts/ChartStatistic.vue`
  - 用途: 静态展示关键指标

### 6. 进度展示类
- **circleProgress** - 环形进度条
  - 组件路径: `@/components/Charts/ChartCircleProgress.vue`
  - 用途: 圆形进度展示

- **gauge** - 仪表盘
  - 组件路径: `@/components/Charts/ChartGauge.vue`
  - 用途: 仪表盘样式的进度展示

- **lineProgress** - 线性进度条
  - 组件路径: `@/components/Charts/ChartLineProgress.vue`
  - 用途: 线性进度展示

## 使用方法

### 1. 获取图表组件
```javascript
import { chartComponentMap } from './chartComponents.js'

// 动态加载组件
const loadChart = async (chartType) => {
  const componentLoader = chartComponentMap[chartType]
  const component = await componentLoader()
  return component.default || component
}
```

### 2. 获取静态测试数据
```javascript
import { getStaticPreviewData } from './chartComponents.js'

// 获取指定图表类型的测试数据
const data = getStaticPreviewData('bar')
```

### 3. 获取组件属性配置
```javascript
import { getChartProps } from './chartComponents.js'

// 获取组件属性
const props = getChartProps('bar', data)
```

### 4. 获取图表类型信息
```javascript
import { chartTypeConfig } from './chartComponents.js'

// 获取图表类型配置
const config = chartTypeConfig['bar']
// { name: "柱状图", icon: "bar-chart", category: "基础图表" }
```

## 完整使用示例

```vue
<template>
  <component
    :is="chartComponent"
    v-bind="chartProps"
    v-if="chartComponent"
  />
</template>

<script>
import { ref, onMounted } from 'vue'
import { 
  chartComponentMap, 
  getStaticPreviewData, 
  getChartProps 
} from './chartComponents.js'

export default {
  setup() {
    const chartComponent = ref(null)
    const chartProps = ref({})

    const loadChart = async (chartType) => {
      // 加载组件
      const componentLoader = chartComponentMap[chartType]
      chartComponent.value = await componentLoader()

      // 获取数据和属性
      const data = getStaticPreviewData(chartType)
      chartProps.value = getChartProps(chartType, data)
    }

    onMounted(() => {
      loadChart('bar') // 加载柱状图
    })

    return {
      chartComponent,
      chartProps
    }
  }
}
</script>
```

## 数据格式说明

每种图表类型都有对应的数据格式要求，详细格式请参考 `getStaticPreviewData` 函数中的示例数据。

## 预览组件

使用 `ChartPreview.vue` 组件可以预览所有12种图表类型的效果和配置。
