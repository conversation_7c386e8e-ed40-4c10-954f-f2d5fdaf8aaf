<template>
  <div class="chart-line-progress" :style="{ width, height }">
    <div class="progress-list">
      <div 
        v-for="(item, index) in progressItems" 
        :key="index" 
        class="progress-item"
        :class="{ 'progress-item-vertical': layout === 'vertical' }"
      >
        <!-- 标签 -->
        <div class="progress-label" :style="labelStyle">
          {{ item.label }}
        </div>
        
        <!-- 进度条容器 -->
        <div class="progress-bar-container" :style="containerStyle">
          <!-- 背景条 -->
          <div 
            class="progress-bar-bg" 
            :style="{ 
              height: `${strokeWidth}px`,
              backgroundColor: backgroundColor,
              borderRadius: `${strokeWidth / 2}px`
            }"
          >
            <!-- 进度条 -->
            <div 
              class="progress-bar" 
              :class="{ 'animate': animation }"
              :style="{ 
                width: `${getPercentage(item)}%`,
                height: '100%',
                backgroundColor: getColor(item, index),
                borderRadius: `${strokeWidth / 2}px`,
                transition: animation ? `width ${animationDuration}ms ease` : 'none'
              }"
            ></div>
          </div>
          
          <!-- 数值显示 -->
          <div v-if="showLabel" class="progress-value" :style="valueStyle">
            {{ formatValue(item) }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ChartLineProgress',
  props: {
    // 基础属性
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '200px'
    },
    // 数据配置
    items: {
      type: Array,
      default: () => []
    },
    // 样式配置
    colors: {
      type: Array,
      default: () => ['#52c41a', '#1890ff', '#faad14', '#f5222d', '#722ed1']
    },
    backgroundColor: {
      type: String,
      default: '#f0f0f0'
    },
    strokeWidth: {
      type: Number,
      default: 8
    },
    // 布局配置
    layout: {
      type: String,
      default: 'vertical' // 'vertical', 'horizontal'
    },
    // 显示配置
    showLabel: {
      type: Boolean,
      default: true
    },
    // 样式自定义
    labelStyle: {
      type: Object,
      default: () => ({
        color: '#666666',
        fontSize: '14px',
        marginBottom: '8px',
        fontWeight: 'normal'
      })
    },
    valueStyle: {
      type: Object,
      default: () => ({
        color: '#262626',
        fontSize: '12px',
        fontWeight: 'bold',
        marginLeft: '8px'
      })
    },
    containerStyle: {
      type: Object,
      default: () => ({
        marginBottom: '16px'
      })
    },
    // 格式化配置
    formatter: {
      type: Function,
      default: null
    },
    // 动画配置
    animation: {
      type: Boolean,
      default: true
    },
    animationDuration: {
      type: Number,
      default: 1000
    },
    animationDelay: {
      type: Number,
      default: 100
    }
  },
  data() {
    return {
      animatedItems: []
    }
  },
  computed: {
    progressItems() {
      return this.animatedItems.length > 0 ? this.animatedItems : this.items
    }
  },
  watch: {
    items: {
      handler(newItems) {
        if (this.animation) {
          this.animateItems(newItems)
        } else {
          this.animatedItems = [...newItems]
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    animateItems(targetItems) {
      // 初始化动画项目
      if (this.animatedItems.length === 0) {
        this.animatedItems = targetItems.map(item => ({
          ...item,
          value: 0
        }))
      }
      
      // 逐个动画
      targetItems.forEach((targetItem, index) => {
        setTimeout(() => {
          this.animateItem(index, targetItem.value)
        }, index * this.animationDelay)
      })
    },
    animateItem(index, targetValue) {
      const startValue = this.animatedItems[index]?.value || 0
      const endValue = Number(targetValue)
      const duration = this.animationDuration
      const startTime = Date.now()
      
      const animate = () => {
        const now = Date.now()
        const progress = Math.min((now - startTime) / duration, 1)
        
        // 使用缓动函数
        const easeProgress = this.easeOutCubic(progress)
        const currentValue = startValue + (endValue - startValue) * easeProgress
        
        // 更新动画项目
        this.$set(this.animatedItems, index, {
          ...this.animatedItems[index],
          value: currentValue
        })
        
        if (progress < 1) {
          requestAnimationFrame(animate)
        } else {
          this.$set(this.animatedItems, index, {
            ...this.animatedItems[index],
            value: endValue
          })
        }
      }
      
      requestAnimationFrame(animate)
    },
    easeOutCubic(t) {
      return 1 - Math.pow(1 - t, 3)
    },
    getPercentage(item) {
      const value = Number(item.value || 0)
      const max = Number(item.max || 100)
      return Math.min(Math.max((value / max) * 100, 0), 100)
    },
    getColor(item, index) {
      if (item.color) {
        return item.color
      }
      return this.colors[index % this.colors.length]
    },
    formatValue(item) {
      const value = Number(item.value || 0)
      
      // 自定义格式化函数
      if (this.formatter && typeof this.formatter === 'function') {
        return this.formatter(value, item)
      }
      
      // 默认格式化
      const unit = item.unit || '%'
      const precision = item.precision !== undefined ? item.precision : 1
      
      return `${Number(value).toFixed(precision)}${unit}`
    },
    // 公共方法
    updateItems(newItems) {
      if (this.animation) {
        this.animateItems(newItems)
      } else {
        this.animatedItems = [...newItems]
      }
    },
    updateItem(index, newValue) {
      if (this.animation) {
        this.animateItem(index, newValue)
      } else {
        this.$set(this.animatedItems, index, {
          ...this.animatedItems[index],
          value: newValue
        })
      }
    },
    reset() {
      this.animatedItems = this.items.map(item => ({
        ...item,
        value: 0
      }))
    }
  }
}
</script>

<style scoped>
.chart-line-progress {
  position: relative;
  padding: 16px;
  box-sizing: border-box;
}

.progress-list {
  height: 100%;
  overflow-y: auto;
}

.progress-item {
  margin-bottom: 16px;
}

.progress-item:last-child {
  margin-bottom: 0;
}

.progress-item-vertical {
  display: flex;
  flex-direction: column;
}

.progress-label {
  color: #666666;
  font-size: 14px;
  margin-bottom: 8px;
  font-weight: normal;
}

.progress-bar-container {
  display: flex;
  align-items: center;
  position: relative;
}

.progress-bar-bg {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.progress-bar {
  position: relative;
  height: 100%;
  border-radius: inherit;
  background: linear-gradient(90deg, currentColor 0%, currentColor 100%);
}

.progress-bar.animate {
  transition: width 1s ease;
}

.progress-value {
  color: #262626;
  font-size: 12px;
  font-weight: bold;
  margin-left: 8px;
  min-width: 50px;
  text-align: right;
}

/* 水平布局 */
.progress-list.horizontal {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.progress-list.horizontal .progress-item {
  flex: 1;
  min-width: 200px;
  margin-bottom: 0;
}

/* 渐变效果 */
.progress-bar.gradient {
  background: linear-gradient(90deg, #52c41a 0%, #73d13d 100%);
}

/* 条纹效果 */
.progress-bar.striped {
  background-image: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.15) 25%,
    transparent 25%,
    transparent 50%,
    rgba(255, 255, 255, 0.15) 50%,
    rgba(255, 255, 255, 0.15) 75%,
    transparent 75%,
    transparent
  );
  background-size: 1rem 1rem;
}

/* 动画条纹 */
.progress-bar.striped.animate {
  animation: progress-bar-stripes 1s linear infinite;
}

@keyframes progress-bar-stripes {
  0% {
    background-position: 1rem 0;
  }
  100% {
    background-position: 0 0;
  }
}

/* 发光效果 */
.progress-bar.glow {
  box-shadow: 0 0 10px currentColor;
}

/* 脉冲效果 */
.progress-bar.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chart-line-progress {
    padding: 12px;
  }
  
  .progress-label {
    font-size: 12px;
  }
  
  .progress-value {
    font-size: 10px;
    min-width: 40px;
  }
  
  .progress-item {
    margin-bottom: 12px;
  }
}

/* 主题变体 */
.progress-item.success .progress-bar {
  background-color: #52c41a;
}

.progress-item.warning .progress-bar {
  background-color: #faad14;
}

.progress-item.error .progress-bar {
  background-color: #f5222d;
}

.progress-item.info .progress-bar {
  background-color: #1890ff;
}
</style>
