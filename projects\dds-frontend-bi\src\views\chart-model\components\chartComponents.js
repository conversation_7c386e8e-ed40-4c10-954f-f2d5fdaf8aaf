// 图表组件映射配置 - 12种图表类型
export const chartComponentMap = {
  // 1. 表格组件
  table: () => import("./charts/Table.vue"),

  // 2. 柱状图
  bar: () => import("./charts/ChartColumn.vue"),

  // 3. 条形图
  horizontalBar: () => import("./charts/ChartBar.vue"),

  // 4. 折线图
  line: () => import("./charts/ChartLine.vue"),

  // 5. 双轴图（折线+柱状）
  lineBar: () => import("./charts/ChartDoubleAxis.vue"),

  // 6. 饼图
  pie: () => import("./charts/ChartPie.vue"),

  // 7. 雷达图
  radar: () => import("./charts/ChartRadar.vue"),

  // 8. 翻牌器
  flop: () => import("./charts/ChartFlipper.vue"),

  // 9. 环形进度条
  circleProgress: () => import("./charts/ChartCircleProgress.vue"),

  // 10. 仪表盘
  gauge: () => import("./charts/ChartGauge.vue"),

  // 11. 统计数值
  statistic: () => import("./charts/ChartStatistic.vue"),

  // 12. 线性进度条
  lineProgress: () => import("./charts/ChartLineProgress.vue")
}

// 静态预览数据 - 为12种图表类型提供测试数据
export const getStaticPreviewData = chartType => {
  // 基础数据 - 用于柱状图、条形图、饼图等
  const baseData = [
    {
      name: "产品A",
      value: 85,
      category: "电子产品",
      score: 85,
      percentage: 23.5
    },
    {
      name: "产品B",
      value: 92,
      category: "家居用品",
      score: 92,
      percentage: 25.4
    },
    {
      name: "产品C",
      value: 78,
      category: "服装配饰",
      score: 78,
      percentage: 21.6
    },
    {
      name: "产品D",
      value: 88,
      category: "食品饮料",
      score: 88,
      percentage: 24.3
    },
    {
      name: "产品E",
      value: 65,
      category: "图书文具",
      score: 65,
      percentage: 18.0
    }
  ]

  // 统计数据 - 用于统计组件、进度条等
  const statisticData = [
    {
      label: "总销售额",
      value: 1234567,
      unit: "元",
      target: 1500000,
      percentage: 82.3
    },
    { label: "增长率", value: 15.6, unit: "%", target: 20, percentage: 78.0 },
    { label: "完成率", value: 89.2, unit: "%", target: 100, percentage: 89.2 },
    {
      label: "用户数",
      value: 45678,
      unit: "人",
      target: 50000,
      percentage: 91.4
    }
  ]

  switch (chartType) {
    // 1. 表格组件
    case "table":
      return {
        headList: [
          {
            prop: "date",
            label: "日期"
          },
          {
            prop: "name",
            label: "姓名"
          },
          {
            prop: "address",
            label: "地址"
          }
        ],
        tableData: [
          {
            date: "2016-05-02",
            name: "王小虎",
            address: "上海市普陀区金沙江路 1518 弄"
          },
          {
            date: "2016-05-04",
            name: "王小虎",
            address: "上海市普陀区金沙江路 1517 弄"
          },
          {
            date: "2016-05-01",
            name: "王小虎",
            address: "上海市普陀区金沙江路 1519 弄"
          },
          {
            date: "2016-05-03",
            name: "王小虎",
            address: "上海市普陀区金沙江路 1516 弄"
          }
        ]
      }

    // 2. 柱状图
    case "bar":
      return {
        categories: [
          "2022-01",
          "2022-02",
          "2022-03",
          "2022-04",
          "2022-05",
          "2022-06"
        ],
        series: [
          {
            name: "男生",
            data: [126, 355, 555, 844, 122, 911]
          },
          {
            name: "女生",
            data: [156, 325, 535, 544, 222, 191]
          }
        ]
      }

    // 3. 条形图
    case "horizontalBar":
      return {
        categories: [
          "2022-01",
          "2022-02",
          "2022-03",
          "2022-04",
          "2022-05",
          "2022-06"
        ],
        series: [
          {
            name: "男生",
            data: [126, 355, 555, 844, 122, 911]
          },
          {
            name: "女生",
            data: [156, 325, 535, 544, 222, 191]
          }
        ]
      }

    // 4. 折线图
    case "line":
      return {
        categories: [
          "2022-01",
          "2022-02",
          "2022-03",
          "2022-04",
          "2022-05",
          "2022-06"
        ],
        series: [
          {
            name: "男生",
            data: [126, 355, 555, 844, 122, 911]
          },
          {
            name: "女生",
            data: [156, 325, 535, 544, 222, 191]
          }
        ]
      }

    // 5. 双轴图（折线+柱状）
    case "lineBar":
      return {
        categories: ["石家庄", "北京", "上海", "武汉", "成都", "重庆", "南京"],
        series: [
          {
            name: "降雨量",
            type: "bar",
            data: [610, 890, 456, 778, 754, 666, 135]
          },
          {
            name: "平均温度",
            type: "line",
            data: [36, 41, 43, 47, 31, 49, 39]
          }
        ]
      }

    // 6. 饼图
    case "pie":
      return [
        ["名称", "销量"],
        ["家具家电", 610],
        ["粮油副食", 890],
        ["美容洗护", 456],
        ["母婴用品", 778],
        ["进口食品", 754],
        ["食品饮料", 666],
        ["家庭清洁", 125]
      ]

    // 7. 雷达图
    case "radar":
      return {
        indicator: [
          {
            name: "防御",
            axisLabel: {
              show: true
            }
          },
          {
            name: "速度",
            axisLabel: {
              show: false
            }
          },
          {
            name: "攻击",
            axisLabel: {
              show: false
            }
          },
          {
            name: "躲闪",
            axisLabel: {
              show: false
            }
          },
          {
            name: "穿透",
            axisLabel: {
              show: false
            }
          }
        ],
        series: [
          {
            data: [
              {
                value: [750, 800, 300, 400, 190],
                name: "王昭君"
              },
              {
                value: [655, 123, 222, 666, 900],
                name: "貂蝉"
              }
            ]
          }
        ]
      }

    // 8. 翻牌器
    case "flop":
      return {
        value: "123450"
      }

    // 9. 环形进度条
    case "circleProgress":
      return {
        label: "人数增涨",
        value: 50,
        data: 25
      }

    // 10. 仪表盘
    case "gauge":
      return {
        data: { value: statisticData[1].value, max: 100 },
        value: statisticData[1].value,
        max: 100,
        unit: "%",
        startAngle: -90,
        endAngle: 270,
        color: "#faad14"
      }

    // 11. 统计数值
    case "statistic":
      return {
        data: statisticData[0],
        value: statisticData[0].value,
        label: statisticData[0].label,
        unit: statisticData[0].unit,
        precision: 0,
        valueStyle: { color: "#1890ff", fontSize: "24px", fontWeight: "bold" }
      }

    // 12. 线性进度条
    case "lineProgress":
      return {
        data: statisticData,
        items: statisticData.map(item => ({
          label: item.label,
          value: item.percentage,
          max: 100,
          unit: "%",
          color:
            item.percentage >= 90
              ? "#52c41a"
              : item.percentage >= 70
              ? "#faad14"
              : "#f5222d"
        }))
      }

    default:
      return {
        data: baseData,
        xField: "name",
        yField: "value",
        seriesName: "默认数据"
      }
  }
}

// 图表组件属性配置 - 为12种图表类型提供完整的属性配置
export const getChartProps = (chartType, data) => {
  const commonProps = {
    width: "100%",
    height: "300px"
  }

  switch (chartType) {
    // 1. 表格组件
    case "table":
      return {
        ...commonProps,
        height: "400px",
        tableData: data.tableData || data.data,
        tableColumns: data.columns,
        headList: data.headList,
        showSelection: false,
        showBatchTag: false,
        loading: false,
        stripe: true,
        border: true,
        size: "small"
      }

    // 2. 柱状图
    case "bar":
      return {
        ...commonProps,
        // 支持新格式数据
        categories: data.categories,
        series: data.series,
        // 兼容旧格式数据
        chartData: data.data,
        xField: data.xField,
        yField: data.yField,
        seriesName: data.seriesName,
        color: data.color,
        showLabel: true,
        animation: true
      }

    // 3. 条形图
    case "horizontalBar":
      return {
        ...commonProps,
        // 支持新格式数据
        categories: data.categories,
        series: data.series,
        // 兼容旧格式数据
        chartData: data.data,
        xField: data.xField,
        yField: data.yField,
        seriesName: data.seriesName,
        color: data.color,
        showLabel: true,
        animation: true
      }

    // 4. 折线图
    case "line":
      return {
        ...commonProps,
        // 支持新格式数据
        categories: data.categories,
        series: data.series,
        // 兼容旧格式数据
        chartData: data.data,
        xField: data.xField,
        yField: data.yField,
        seriesName: data.seriesName,
        smooth: data.smooth,
        color: data.color,
        showPoint: true,
        animation: true
      }

    // 5. 双轴图（折线+柱状）
    case "lineBar":
      return {
        ...commonProps,
        // 支持新格式数据
        categories: data.categories,
        series: data.series,
        // 兼容旧格式数据
        chartData: data.data,
        xField: data.xField,
        yField: data.yField,
        seriesName: data.seriesName,
        colors: data.colors,
        animation: true
      }

    // 6. 饼图
    case "pie":
      return {
        ...commonProps,
        // 支持新格式数据（二维数组）
        arrayData: Array.isArray(data) ? data : undefined,
        // 兼容旧格式数据
        chartData: data.data,
        colorField: data.colorField,
        angleField: data.angleField,
        seriesName: data.seriesName,
        innerRadius: data.innerRadius,
        colors: data.colors,
        showLabel: true,
        animation: true
      }

    // 7. 雷达图
    case "radar":
      return {
        ...commonProps,
        // 支持新格式数据
        indicator: data.indicator,
        series: data.series,
        // 兼容旧格式数据
        chartData: data.data,
        angleField: data.angleField,
        radiusField: data.radiusField,
        seriesName: data.seriesName,
        color: data.color,
        showLabel: true,
        animation: true
      }

    // 8. 翻牌器
    case "flop":
      return {
        ...commonProps,
        height: "120px",
        value: data.value || data,
        animation: true
      }

    // 9. 环形进度条
    case "circleProgress":
      return {
        ...commonProps,
        height: "200px",
        label: data.label,
        value: data.value,
        data: data.data,
        animation: true
      }

    // 10. 仪表盘
    case "gauge":
      return {
        ...commonProps,
        height: "250px",
        value: data.value,
        max: data.max,
        unit: data.unit,
        startAngle: data.startAngle,
        endAngle: data.endAngle,
        color: data.color,
        animation: true
      }

    // 11. 统计数值
    case "statistic":
      return {
        ...commonProps,
        height: "120px",
        value: data.value,
        label: data.label,
        unit: data.unit,
        precision: data.precision,
        valueStyle: data.valueStyle,
        animation: true
      }

    // 12. 线性进度条
    case "lineProgress":
      return {
        ...commonProps,
        height: "200px",
        items: data.items,
        showLabel: true,
        animation: true
      }

    default:
      return {
        ...commonProps,
        chartData: data.data,
        xField: data.xField,
        yField: data.yField,
        seriesName: data.seriesName
      }
  }
}

// 图表类型配置信息
export const chartTypeConfig = {
  table: { name: "表格", icon: "table", category: "数据展示" },
  bar: { name: "柱状图", icon: "bar-chart", category: "基础图表" },
  horizontalBar: {
    name: "条形图",
    icon: "bar-chart-horizontal",
    category: "基础图表"
  },
  line: { name: "折线图", icon: "line-chart", category: "基础图表" },
  lineBar: { name: "双轴图", icon: "combo-chart", category: "复合图表" },
  pie: { name: "饼图", icon: "pie-chart", category: "基础图表" },
  radar: { name: "雷达图", icon: "radar-chart", category: "特殊图表" },
  flop: { name: "翻牌器", icon: "number", category: "数值展示" },
  circleProgress: {
    name: "环形进度条",
    icon: "progress-circle",
    category: "进度展示"
  },
  gauge: { name: "仪表盘", icon: "gauge", category: "进度展示" },
  statistic: { name: "统计数值", icon: "statistic", category: "数值展示" },
  lineProgress: {
    name: "线性进度条",
    icon: "progress-line",
    category: "进度展示"
  }
}
